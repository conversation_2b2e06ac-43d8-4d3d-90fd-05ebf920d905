# Panduan Instalasi PHP Twibonize

## Langkah-langkah Instalasi Lengkap

### 1. Persiapan Environment

#### Install XAMPP
1. Download XAMPP dari https://www.apachefriends.org/
2. Install XAMPP dengan komponen:
   - Apache
   - MySQL
   - PHP 8.0+
   - phpMyAdmin

#### Install FFmpeg
1. Download FFmpeg dari https://ffmpeg.org/download.html
2. Extract ke folder (misal: `C:\ffmpeg\`)
3. Tambahkan ke PATH sistem:
   - Windows: Control Panel → System → Advanced → Environment Variables
   - Tambahkan `C:\ffmpeg\bin` ke PATH
4. Test di Command Prompt: `ffmpeg -version`

### 2. Setup Aplikasi

#### Copy Files
1. Copy semua file aplikasi ke `C:\xampp\htdocs\php-twibonize\`
2. Pastikan struktur folder sesuai dengan yang ada di README.md

#### Setup Database
1. Start Apache dan MySQL di XAMPP Control Panel
2. Buka http://localhost/phpmyadmin
3. Buat database baru: `php_twibonize`
4. Set collation: `utf8mb4_general_ci`

#### Konfigurasi Database
Edit file `config/database.php` jika diperlukan:
```php
private $host = "localhost";
private $db_name = "php_twibonize";
private $username = "root";
private $password = "";
```

### 3. Instalasi Aplikasi

#### Jalankan Setup
1. Buka browser
2. Akses: http://localhost/php-twibonize/setup.php
3. Setup akan otomatis:
   - Membuat tabel database
   - Membuat folder yang diperlukan
   - Insert data template contoh
   - Validasi sistem

#### Verifikasi Instalasi
1. Akses: http://localhost/php-twibonize/test.php
2. Periksa semua test berhasil (✅)
3. Perbaiki jika ada yang gagal (❌)

### 4. Setup Template Video

#### Opsi 1: Gunakan Demo Template
1. Akses: http://localhost/php-twibonize/demo_template_creator.php
2. Akan membuat template video sederhana untuk testing

#### Opsi 2: Upload Template Sendiri
1. Siapkan file video MP4 (1920x1080, H.264)
2. Copy ke folder: `assets/templates/videos/`
3. Buat gambar preview (400x300 px)
4. Copy ke folder: `assets/templates/previews/`
5. Update database sesuai nama file

### 5. Testing Aplikasi

#### Test Upload
1. Akses: http://localhost/php-twibonize/
2. Upload foto test (JPG/PNG, max 5MB)
3. Pilih template video
4. Atur pengaturan (ukuran, transparansi)
5. Klik "Buat Video Twibbon"
6. Tunggu proses selesai
7. Download hasil video

## Troubleshooting

### Error "FFmpeg not found"
**Solusi:**
- Pastikan FFmpeg sudah terinstall
- Cek PATH sistem sudah benar
- Test di command prompt: `ffmpeg -version`
- Atau copy ffmpeg.exe ke folder aplikasi

### Error "Connection failed"
**Solusi:**
- Pastikan MySQL berjalan di XAMPP
- Cek konfigurasi database.php
- Pastikan database php_twibonize sudah dibuat

### Error "Permission denied"
**Solusi:**
- Set permission folder uploads/ menjadi 777
- Di Windows: Properties → Security → Full Control
- Atau jalankan XAMPP sebagai Administrator

### Error "Upload failed"
**Solusi:**
- Cek ukuran file (max 5MB)
- Cek format file (JPG, PNG, GIF only)
- Cek php.ini settings:
  ```
  upload_max_filesize = 5M
  post_max_size = 10M
  max_execution_time = 300
  ```

### Video processing stuck
**Solusi:**
- Cek log error PHP
- Pastikan template video ada dan valid
- Cek FFmpeg berfungsi normal
- Restart Apache jika perlu

### Template tidak muncul
**Solusi:**
- Cek data di tabel video_templates
- Pastikan file video dan preview ada
- Jalankan setup.php lagi jika perlu

## Konfigurasi Lanjutan

### Optimisasi Performance
1. Edit php.ini:
   ```
   memory_limit = 512M
   max_execution_time = 600
   max_input_time = 600
   ```

2. Enable OPcache:
   ```
   opcache.enable=1
   opcache.memory_consumption=128
   ```

### Keamanan
1. Ganti password database default
2. Set permission folder yang tepat
3. Enable HTTPS jika production
4. Backup database secara berkala

### Maintenance
1. Setup cron job untuk cleanup:
   ```
   0 2 * * * php /path/to/cleanup.php
   ```

2. Monitor disk space folder uploads/
3. Backup database mingguan

## FAQ

**Q: Bisakah menggunakan format video selain MP4?**
A: Saat ini hanya mendukung MP4. FFmpeg bisa convert format lain ke MP4.

**Q: Berapa maksimal durasi video template?**
A: Disarankan 5-15 detik untuk performa optimal.

**Q: Bisakah menambah template baru?**
A: Ya, upload file video dan preview, lalu tambah record di database.

**Q: Apakah bisa dijalankan di hosting shared?**
A: Ya, asalkan hosting mendukung PHP 8, MySQL, dan FFmpeg.

**Q: Bagaimana cara backup data?**
A: Export database dan copy folder uploads/ secara berkala.

## Support

Jika mengalami masalah:
1. Cek file test.php untuk diagnosis
2. Periksa log error PHP
3. Pastikan semua requirement terpenuhi
4. Buat issue di repository jika perlu
