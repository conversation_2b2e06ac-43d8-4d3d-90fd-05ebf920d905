# PHP Twibonize

Aplikasi web berbasis PHP 8 yang memungkinkan pengguna untuk menggabungkan foto mereka dengan template video, mirip dengan aplikasi Twibbonize.

## Fitur

- ✅ Upload foto pengguna dengan validasi format dan ukuran
- ✅ Pilihan template video yang beragam
- ✅ Pengaturan ukuran dan transparansi foto
- ✅ Preview foto dan template sebelum diproses
- ✅ Pemrosesan video menggunakan FFmpeg
- ✅ Progress monitoring real-time
- ✅ Download hasil video
- ✅ Interface responsif dengan Bootstrap 5
- ✅ Sistem antrian pemrosesan
- ✅ Auto cleanup file lama

## Persyaratan Sistem

- PHP 8.0 atau lebih tinggi
- MySQL/MariaDB
- FFmpeg (untuk pemrosesan video)
- XAMPP/WAMP/LAMP
- Ekstensi PHP: PDO, GD, fileinfo

## Instalasi

1. **Clone atau download aplikasi ini ke folder htdocs XAMPP**
   ```
   C:\xampp\htdocs\php-twibonize\
   ```

2. **Buat database MySQL**
   - Buka phpMyAdmin
   - Buat database baru dengan nama `php_twibonize`

3. **Konfigurasi database**
   - Edit file `config/database.php` jika diperlukan
   - Sesuaikan username, password, dan nama database

4. **Install FFmpeg**
   - Download FFmpeg dari https://ffmpeg.org/download.html
   - Extract dan tambahkan ke PATH sistem
   - Atau letakkan ffmpeg.exe di folder aplikasi

5. **Jalankan setup**
   - Buka browser dan akses `http://localhost/php-twibonize/setup.php`
   - Setup akan membuat tabel database dan direktori yang diperlukan

6. **Siapkan template video**
   - Letakkan file video template di `assets/templates/videos/`
   - Letakkan gambar preview di `assets/templates/previews/`
   - Nama file harus sesuai dengan yang ada di database

## Struktur Direktori

```
php-twibonize/
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── script.js
│   └── templates/
│       ├── videos/          # File video template
│       └── previews/        # Gambar preview template
├── config/
│   └── database.php         # Konfigurasi database
├── includes/
│   └── functions.php        # Fungsi-fungsi utama
├── uploads/
│   ├── photos/              # Upload foto pengguna
│   ├── videos/              # Hasil video yang diproses
│   └── temp/                # File sementara
├── index.php                # Halaman utama
├── process.php              # Handler upload dan antrian
├── process_video.php        # Pemroses video
├── status.php               # API status pemrosesan
├── setup.php                # Setup aplikasi
├── cleanup.php              # Pembersihan file lama
└── README.md
```

## Cara Penggunaan

1. **Akses aplikasi**
   - Buka `http://localhost/php-twibonize/`

2. **Upload foto**
   - Pilih foto (JPG, PNG, GIF, max 5MB)
   - Foto akan ditampilkan di preview

3. **Pilih template**
   - Pilih template video dari dropdown
   - Preview template akan ditampilkan

4. **Atur pengaturan**
   - Sesuaikan ukuran foto (10-100%)
   - Atur transparansi (10-100%)

5. **Proses video**
   - Klik "Buat Video Twibbon"
   - Tunggu proses selesai (progress bar akan menunjukkan status)
   - Download hasil video

## Template Video

Template video harus memiliki spesifikasi:
- Format: MP4
- Resolusi: 1920x1080 (Full HD)
- Durasi: 5-15 detik (optimal)
- Codec: H.264

Posisi foto pada video dapat dikonfigurasi melalui database di tabel `video_templates`:
- `photo_position_x`: Posisi horizontal foto
- `photo_position_y`: Posisi vertikal foto
- `photo_width`: Lebar area foto
- `photo_height`: Tinggi area foto

## API Endpoints

- `POST /process.php` - Upload foto dan buat antrian
- `POST /process_video.php` - Proses video
- `GET /status.php?userVideoId=X` - Cek status pemrosesan
- `GET /cleanup.php` - Bersihkan file lama

## Troubleshooting

### FFmpeg tidak terdeteksi
**Gejala:** Test.php menunjukkan "FFmpeg tidak terdeteksi" meskipun `ffmpeg -version` berfungsi di Command Prompt.

**Penyebab:** PHP web server tidak dapat mengakses FFmpeg karena masalah PATH atau permission.

**Solusi:**
1. **Restart XAMPP** setelah menambah FFmpeg ke PATH
2. **Jalankan XAMPP sebagai Administrator**
3. **Copy ffmpeg.exe** ke folder aplikasi sebagai solusi cepat
4. **Gunakan FFmpeg Troubleshoot Tool**: Akses `ffmpeg_troubleshoot.php` untuk diagnosis detail

**Langkah detail:**
```bash
# Test di Command Prompt
ffmpeg -version

# Jika berhasil tapi PHP tidak detect:
# 1. Copy ffmpeg.exe ke folder aplikasi, ATAU
# 2. Restart XAMPP sebagai Administrator, ATAU
# 3. Set PATH di php.ini atau .htaccess
```

### Error koneksi database
- Periksa konfigurasi di `config/database.php`
- Pastikan MySQL/MariaDB berjalan
- Pastikan database `php_twibonize` sudah dibuat

### Upload gagal
- Periksa permission folder `uploads/`
- Pastikan ukuran file tidak melebihi 5MB
- Periksa format file (hanya JPG, PNG, GIF)

### Video tidak diproses
- Periksa log error PHP
- Pastikan FFmpeg berfungsi dengan baik
- Periksa ketersediaan file template video

## Kustomisasi

### Menambah template baru
1. Upload file video ke `assets/templates/videos/`
2. Upload gambar preview ke `assets/templates/previews/`
3. Tambah record baru di tabel `video_templates`

### Mengubah pengaturan
- Ukuran maksimal upload: Edit `includes/functions.php`
- Durasi cleanup: Edit parameter di `cleanup.php`
- Styling: Edit `assets/css/style.css`

## Keamanan

- Validasi file upload (tipe dan ukuran)
- Sanitasi input pengguna
- Session-based processing
- Auto cleanup file lama
- Error handling yang proper

## Lisensi

Aplikasi ini dibuat untuk tujuan edukasi dan dapat digunakan secara bebas.

## Kontribusi

Silakan buat issue atau pull request untuk perbaikan dan penambahan fitur.

## Changelog

### v1.0.0
- Rilis awal dengan fitur dasar
- Upload foto dan pilih template
- Pemrosesan video dengan FFmpeg
- Interface web responsif
- Sistem antrian dan monitoring progress
