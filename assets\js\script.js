// JavaScript untuk PHP Twibonize

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const form = document.getElementById('twibbonForm');
    const userPhotoInput = document.getElementById('userPhoto');
    const templateSelect = document.getElementById('template');
    const photoSizeRange = document.getElementById('photoSize');
    const photoOpacityRange = document.getElementById('photoOpacity');
    const photoSizeValue = document.getElementById('photoSizeValue');
    const photoOpacityValue = document.getElementById('photoOpacityValue');
    const photoPreview = document.getElementById('photoPreview');
    const photoPreviewImg = document.getElementById('photoPreviewImg');
    const templatePreview = document.getElementById('templatePreview');
    const templatePreviewImg = document.getElementById('templatePreviewImg');
    const progressSection = document.getElementById('progressSection');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const resultSection = document.getElementById('resultSection');
    const resultVideo = document.getElementById('resultVideo');
    const resultVideoSource = document.getElementById('resultVideoSource');
    const downloadLink = document.getElementById('downloadLink');

    // Update range values
    photoSizeRange.addEventListener('input', function() {
        photoSizeValue.textContent = this.value + '%';
    });

    photoOpacityRange.addEventListener('input', function() {
        photoOpacityValue.textContent = this.value + '%';
    });

    // Photo preview
    userPhotoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('Format file tidak didukung. Gunakan JPG, PNG, atau GIF.');
                this.value = '';
                photoPreview.style.display = 'none';
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Ukuran file terlalu besar. Maksimal 5MB.');
                this.value = '';
                photoPreview.style.display = 'none';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                photoPreviewImg.src = e.target.result;
                photoPreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            photoPreview.style.display = 'none';
        }
    });

    // Template preview
    templateSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const previewImage = selectedOption.getAttribute('data-preview');
            if (previewImage) {
                templatePreviewImg.src = previewImage;
                templatePreview.style.display = 'block';
            }
        } else {
            templatePreview.style.display = 'none';
        }
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate form
        if (!userPhotoInput.files[0]) {
            alert('Silakan pilih foto terlebih dahulu.');
            return;
        }

        if (!templateSelect.value) {
            alert('Silakan pilih template video.');
            return;
        }

        // Show progress section
        progressSection.style.display = 'block';
        resultSection.style.display = 'none';
        
        // Disable form
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        // Create FormData
        const formData = new FormData(form);

        // Upload and process
        fetch('process.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Start processing the video
                processVideo(data.userVideoId);
            } else {
                throw new Error(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan: ' + error.message);
            resetForm();
        });
    });

    function processVideo(userVideoId) {
        // Update progress
        updateProgress(20, 'Memulai pemrosesan video...');

        // Start video processing
        fetch('process_video.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'userVideoId=' + userVideoId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Monitor processing status
                monitorProgress(userVideoId);
            } else {
                throw new Error(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat memproses video: ' + error.message);
            resetForm();
        });
    }

    function monitorProgress(userVideoId) {
        const checkStatus = () => {
            fetch(`status.php?userVideoId=${userVideoId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateProgress(data.progress, getStatusMessage(data.status, data.progress));

                    if (data.video_status === 'completed') {
                        // Video processing completed
                        showResult(data.video_url);
                    } else if (data.video_status === 'failed') {
                        throw new Error(data.error_message || 'Pemrosesan video gagal.');
                    } else {
                        // Continue monitoring
                        setTimeout(checkStatus, 2000);
                    }
                } else {
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Terjadi kesalahan saat memantau progress: ' + error.message);
                resetForm();
            });
        };

        checkStatus();
    }

    function updateProgress(percentage, message) {
        progressBar.style.width = percentage + '%';
        progressBar.textContent = percentage + '%';
        progressText.textContent = message;
    }

    function getStatusMessage(status, progress) {
        switch (status) {
            case 'pending':
                return 'Menunggu antrian...';
            case 'processing':
                if (progress < 30) return 'Memproses foto...';
                if (progress < 80) return 'Menggabungkan dengan video...';
                return 'Menyelesaikan video...';
            case 'completed':
                return 'Video berhasil dibuat!';
            case 'failed':
                return 'Pemrosesan gagal.';
            default:
                return 'Memproses...';
        }
    }

    function showResult(videoUrl) {
        // Hide progress section
        progressSection.style.display = 'none';
        
        // Show result section
        resultVideoSource.src = videoUrl;
        resultVideo.load();
        downloadLink.href = videoUrl;
        downloadLink.download = 'twibbon_video_' + Date.now() + '.mp4';
        resultSection.style.display = 'block';

        // Reset form
        resetForm();
    }

    function resetForm() {
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-magic me-2"></i>Buat Video Twibbon';
    }

    // Template card click handlers
    document.addEventListener('click', function(e) {
        if (e.target.closest('.template-card')) {
            const templateCard = e.target.closest('.template-card');
            const templateName = templateCard.querySelector('h6').textContent;
            
            // Find and select the corresponding option
            for (let option of templateSelect.options) {
                if (option.textContent.trim() === templateName.trim()) {
                    templateSelect.value = option.value;
                    templateSelect.dispatchEvent(new Event('change'));
                    break;
                }
            }
            
            // Scroll to form
            form.scrollIntoView({ behavior: 'smooth' });
        }
    });

    // Drag and drop functionality for photo upload
    const photoUploadArea = userPhotoInput.parentElement;
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        photoUploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        photoUploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        photoUploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        photoUploadArea.classList.add('border-primary');
    }

    function unhighlight(e) {
        photoUploadArea.classList.remove('border-primary');
    }

    photoUploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            userPhotoInput.files = files;
            userPhotoInput.dispatchEvent(new Event('change'));
        }
    }
});
