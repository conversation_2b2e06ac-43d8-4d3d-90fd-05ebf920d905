<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

header('Content-Type: application/json');

// Initialize database connection
$db = new Database();
$conn = $db->getConnection();

if (isset($_GET['userVideoId'])) {
    $userVideoId = intval($_GET['userVideoId']);
    
    try {
        // Get processing status
        $status = getProcessingStatus($conn, $userVideoId);
        $userVideo = getUserVideo($conn, $userVideoId);
        
        if (!$status || !$userVideo) {
            throw new Exception('Data tidak ditemukan.');
        }
        
        $response = [
            'success' => true,
            'status' => $status['status'],
            'progress' => $status['progress'],
            'error_message' => $status['error_message'],
            'video_status' => $userVideo['status']
        ];
        
        // If completed, include video URL
        if ($userVideo['status'] === 'completed') {
            $response['video_url'] = $userVideo['output_video'];
        }
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} else {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Parameter userVideoId diperlukan.'
    ]);
}
?>
