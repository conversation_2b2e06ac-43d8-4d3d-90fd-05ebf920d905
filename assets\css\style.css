/* Custom CSS untuk PHP Twibonize */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

.preview-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.preview-box {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.preview-box img {
    max-height: 180px;
    border-radius: 5px;
}

.template-card {
    background-color: white;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #dee2e6;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.template-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
}

.template-info h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

.progress {
    height: 25px;
    border-radius: 12px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 12px;
    font-weight: 600;
}

.result-video {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.result-video video {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-range {
    margin-bottom: 10px;
}

.form-range::-webkit-slider-thumb {
    background-color: #0d6efd;
}

.form-range::-moz-range-thumb {
    background-color: #0d6efd;
    border: none;
}

#progressSection {
    background-color: #e3f2fd;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #bbdefb;
}

#resultSection {
    background-color: #e8f5e8;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #c8e6c9;
}

.alert {
    border-radius: 10px;
    border: none;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

footer {
    margin-top: auto;
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 20px 15px;
    }
    
    .preview-section {
        margin-top: 20px;
    }
    
    .template-card {
        margin-bottom: 15px;
    }
}

/* Custom file input styling */
.form-control[type="file"] {
    padding: 8px 12px;
    border-radius: 8px;
}

.form-control[type="file"]:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Select styling */
.form-select {
    border-radius: 8px;
}

.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Range input labels */
.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Status indicators */
.status-pending {
    color: #ffc107;
}

.status-processing {
    color: #17a2b8;
}

.status-completed {
    color: #28a745;
}

.status-failed {
    color: #dc3545;
}
