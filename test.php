<?php
/**
 * Test file untuk PHP Twibonize
 * Men<PERSON>ji berbagai fungsi dan komponen aplikasi
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🧪 Test PHP Twibonize</h2>";

// Test 1: Database Connection
echo "<h3>1. Test Koneksi Database</h3>";
try {
    $db = new Database();
    $conn = $db->getConnection();
    if ($conn) {
        echo "<p>✅ Koneksi database berhasil</p>";
        
        // Test query
        $stmt = $conn->query("SELECT COUNT(*) as count FROM video_templates");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ Query database berhasil. Jumlah template: " . $result['count'] . "</p>";
    } else {
        echo "<p>❌ Koneksi database gagal</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Test 2: Directory Structure
echo "<h3>2. Test Struktur Direktori</h3>";
$requiredDirs = [
    'uploads/photos',
    'uploads/videos', 
    'uploads/temp',
    'assets/templates/videos',
    'assets/templates/previews',
    'assets/css',
    'assets/js',
    'config',
    'includes'
];

foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        echo "<p>✅ Direktori '$dir' ada</p>";
    } else {
        echo "<p>❌ Direktori '$dir' tidak ditemukan</p>";
    }
}

// Test 3: File Permissions
echo "<h3>3. Test Permission File</h3>";
$uploadDirs = ['uploads/photos', 'uploads/videos', 'uploads/temp'];
foreach ($uploadDirs as $dir) {
    if (is_writable($dir)) {
        echo "<p>✅ Direktori '$dir' dapat ditulis</p>";
    } else {
        echo "<p>⚠️ Direktori '$dir' tidak dapat ditulis</p>";
    }
}

// Test 4: PHP Extensions
echo "<h3>4. Test PHP Extensions</h3>";
$requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'fileinfo', 'json'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ Extension '$ext' tersedia</p>";
    } else {
        echo "<p>❌ Extension '$ext' tidak tersedia</p>";
    }
}

// Test 5: FFmpeg
echo "<h3>5. Test FFmpeg</h3>";

// Test basic detection
if (validateFFmpegInstallation()) {
    echo "<p>✅ FFmpeg terdeteksi dan siap digunakan</p>";

    // Get FFmpeg path
    $ffmpegPath = getFFmpegPath();
    if ($ffmpegPath) {
        echo "<p>ℹ️ Path FFmpeg: " . htmlspecialchars($ffmpegPath) . "</p>";

        // Get FFmpeg version using detected path
        $output = [];
        $command = (strpos($ffmpegPath, ' ') !== false) ? '"' . $ffmpegPath . '"' : $ffmpegPath;
        exec($command . ' -version 2>&1', $output);
        if (!empty($output[0])) {
            echo "<p>ℹ️ " . htmlspecialchars($output[0]) . "</p>";
        }
    }
} else {
    echo "<p>⚠️ FFmpeg tidak terdeteksi</p>";

    // Debug information
    echo "<h4>Debug Informasi:</h4>";

    // Test direct command
    $output = [];
    $returnCode = 0;
    exec('ffmpeg -version 2>&1', $output, $returnCode);
    echo "<p>🔍 Test 'ffmpeg -version': Return code = $returnCode</p>";
    if (!empty($output)) {
        echo "<p>🔍 Output: " . htmlspecialchars(implode(' | ', array_slice($output, 0, 3))) . "</p>";
    }

    // Test with .exe extension
    $output2 = [];
    $returnCode2 = 0;
    exec('ffmpeg.exe -version 2>&1', $output2, $returnCode2);
    echo "<p>🔍 Test 'ffmpeg.exe -version': Return code = $returnCode2</p>";

    // Check common paths
    $commonPaths = [
        'C:\\ffmpeg\\bin\\ffmpeg.exe',
        'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
        'C:\\Program Files (x86)\\ffmpeg\\bin\\ffmpeg.exe'
    ];

    echo "<p>🔍 Checking common paths:</p>";
    foreach ($commonPaths as $path) {
        if (file_exists($path)) {
            echo "<p>✅ Found: $path</p>";
        } else {
            echo "<p>❌ Not found: $path</p>";
        }
    }

    // Test 'where' command
    $output3 = [];
    $returnCode3 = 0;
    exec('where ffmpeg 2>&1', $output3, $returnCode3);
    echo "<p>🔍 Test 'where ffmpeg': Return code = $returnCode3</p>";
    if (!empty($output3)) {
        echo "<p>🔍 Where output: " . htmlspecialchars(implode(' | ', $output3)) . "</p>";
    }

    echo "<p>💡 <strong>Solusi:</strong></p>";
    echo "<ul>";
    echo "<li>Pastikan FFmpeg sudah terinstall</li>";
    echo "<li>Tambahkan FFmpeg ke PATH sistem Windows</li>";
    echo "<li>Restart XAMPP setelah menambah PATH</li>";
    echo "<li>Atau copy ffmpeg.exe ke folder aplikasi</li>";
    echo "</ul>";
    echo "<p>Download dari: <a href='https://ffmpeg.org/download.html' target='_blank'>https://ffmpeg.org/download.html</a></p>";
}

// Test 6: Template Data
echo "<h3>6. Test Data Template</h3>";
try {
    $templates = getVideoTemplates($conn);
    if (!empty($templates)) {
        echo "<p>✅ Data template berhasil dimuat (" . count($templates) . " template)</p>";
        foreach ($templates as $template) {
            echo "<p>📹 " . htmlspecialchars($template['name']) . " - " . $template['filename'] . "</p>";
        }
    } else {
        echo "<p>⚠️ Tidak ada data template</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error mengambil data template: " . $e->getMessage() . "</p>";
}

// Test 7: Image Processing
echo "<h3>7. Test Image Processing</h3>";
if (extension_loaded('gd')) {
    $gdInfo = gd_info();
    echo "<p>✅ GD Library version: " . $gdInfo['GD Version'] . "</p>";
    echo "<p>✅ JPEG Support: " . ($gdInfo['JPEG Support'] ? 'Ya' : 'Tidak') . "</p>";
    echo "<p>✅ PNG Support: " . ($gdInfo['PNG Support'] ? 'Ya' : 'Tidak') . "</p>";
    echo "<p>✅ GIF Support: " . ($gdInfo['GIF Read Support'] ? 'Ya' : 'Tidak') . "</p>";
} else {
    echo "<p>❌ GD Library tidak tersedia</p>";
}

// Test 8: Session
echo "<h3>8. Test Session</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p>✅ Session aktif</p>";
    echo "<p>ℹ️ Session ID: " . session_id() . "</p>";
} else {
    echo "<p>⚠️ Session tidak aktif</p>";
}

// Test 9: Memory and Execution Limits
echo "<h3>9. Test PHP Configuration</h3>";
echo "<p>ℹ️ Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>ℹ️ Max Execution Time: " . ini_get('max_execution_time') . " detik</p>";
echo "<p>ℹ️ Upload Max Filesize: " . ini_get('upload_max_filesize') . "</p>";
echo "<p>ℹ️ Post Max Size: " . ini_get('post_max_size') . "</p>";

// Test 10: File Upload Test
echo "<h3>10. Test Upload Simulation</h3>";
$testFile = [
    'name' => 'test.jpg',
    'type' => 'image/jpeg',
    'size' => 1024000, // 1MB
    'tmp_name' => '',
    'error' => UPLOAD_ERR_OK
];

// Simulate validation
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
$maxSize = 5 * 1024 * 1024; // 5MB

if (in_array($testFile['type'], $allowedTypes)) {
    echo "<p>✅ Validasi tipe file berhasil</p>";
} else {
    echo "<p>❌ Validasi tipe file gagal</p>";
}

if ($testFile['size'] <= $maxSize) {
    echo "<p>✅ Validasi ukuran file berhasil</p>";
} else {
    echo "<p>❌ Validasi ukuran file gagal</p>";
}

echo "<h3>📋 Ringkasan Test</h3>";
echo "<p>Test selesai. Periksa hasil di atas untuk memastikan semua komponen berfungsi dengan baik.</p>";
echo "<p><a href='index.php' class='btn btn-primary'>Kembali ke Aplikasi</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test PHP Twibonize</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .btn { margin-top: 20px; padding: 10px 20px; }
        h3 { color: #0d6efd; margin-top: 30px; }
    </style>
</head>
<body>
    <!-- Content will be echoed above -->
</body>
</html>
