<?php
class Database {
    private $host = "localhost";
    private $db_name = "php_twibonize";
    private $username = "root";
    private $password = "";
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->exec("set names utf8");
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }

    public function createTables() {
        $queries = [
            "CREATE TABLE IF NOT EXISTS video_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHA<PERSON>(255) NOT NULL,
                filename VARCHAR(255) NOT NULL,
                preview_image VARCHAR(255) NOT NULL,
                duration DECIMAL(5,2) NOT NULL,
                width INT NOT NULL,
                height INT NOT NULL,
                photo_position_x INT DEFAULT 0,
                photo_position_y INT DEFAULT 0,
                photo_width INT DEFAULT 200,
                photo_height INT DEFAULT 200,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            "CREATE TABLE IF NOT EXISTS user_videos (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(255) NOT NULL,
                template_id INT NOT NULL,
                user_photo VARCHAR(255) NOT NULL,
                output_video VARCHAR(255) NOT NULL,
                photo_size INT DEFAULT 50,
                photo_opacity INT DEFAULT 100,
                status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (template_id) REFERENCES video_templates(id)
            )",
            
            "CREATE TABLE IF NOT EXISTS processing_queue (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_video_id INT NOT NULL,
                status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
                progress INT DEFAULT 0,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_video_id) REFERENCES user_videos(id)
            )"
        ];

        foreach ($queries as $query) {
            try {
                $this->conn->exec($query);
            } catch(PDOException $exception) {
                echo "Error creating table: " . $exception->getMessage();
            }
        }
    }

    public function insertSampleTemplates() {
        $templates = [
            [
                'name' => 'Template Hari Kemerdekaan',
                'filename' => 'template_kemerdekaan.mp4',
                'preview_image' => 'assets/templates/previews/kemerdekaan.jpg',
                'duration' => 10.0,
                'width' => 1920,
                'height' => 1080,
                'photo_position_x' => 500,
                'photo_position_y' => 300,
                'photo_width' => 400,
                'photo_height' => 400
            ],
            [
                'name' => 'Template Ulang Tahun',
                'filename' => 'template_birthday.mp4',
                'preview_image' => 'assets/templates/previews/birthday.jpg',
                'duration' => 8.0,
                'width' => 1920,
                'height' => 1080,
                'photo_position_x' => 600,
                'photo_position_y' => 200,
                'photo_width' => 350,
                'photo_height' => 350
            ],
            [
                'name' => 'Template Wisuda',
                'filename' => 'template_graduation.mp4',
                'preview_image' => 'assets/templates/previews/graduation.jpg',
                'duration' => 12.0,
                'width' => 1920,
                'height' => 1080,
                'photo_position_x' => 400,
                'photo_position_y' => 250,
                'photo_width' => 450,
                'photo_height' => 450
            ]
        ];

        $checkQuery = "SELECT COUNT(*) FROM video_templates";
        $stmt = $this->conn->prepare($checkQuery);
        $stmt->execute();
        $count = $stmt->fetchColumn();

        if ($count == 0) {
            $insertQuery = "INSERT INTO video_templates 
                (name, filename, preview_image, duration, width, height, 
                 photo_position_x, photo_position_y, photo_width, photo_height) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->conn->prepare($insertQuery);
            
            foreach ($templates as $template) {
                $stmt->execute([
                    $template['name'],
                    $template['filename'],
                    $template['preview_image'],
                    $template['duration'],
                    $template['width'],
                    $template['height'],
                    $template['photo_position_x'],
                    $template['photo_position_y'],
                    $template['photo_width'],
                    $template['photo_height']
                ]);
            }
        }
    }
}
?>
