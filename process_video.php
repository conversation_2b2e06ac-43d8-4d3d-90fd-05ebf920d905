<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Initialize database connection
$db = new Database();
$conn = $db->getConnection();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['userVideoId'])) {
    $userVideoId = intval($_POST['userVideoId']);
    
    try {
        // Process the video
        $success = processVideo($conn, $userVideoId);
        
        if ($success) {
            echo json_encode([
                'success' => true,
                'message' => 'Video berhasil diproses.'
            ]);
        } else {
            throw new Exception('Gagal memproses video.');
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} else {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Parameter tidak valid.'
    ]);
}
?>
