@echo off
echo ========================================
echo    PHP Twibonize - FFmpeg Setup Helper
echo ========================================
echo.

echo Checking current FFmpeg installation...
ffmpeg -version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] FFmpeg is already installed and accessible!
    ffmpeg -version | findstr "ffmpeg version"
    echo.
    echo You can now use PHP Twibonize without issues.
    pause
    exit /b 0
) else (
    echo [WARNING] FFmpeg not found in PATH
)

echo.
echo ========================================
echo    FFmpeg Installation Options
echo ========================================
echo.
echo 1. Download and install FFmpeg automatically
echo 2. Copy existing ffmpeg.exe to application folder
echo 3. Show manual installation instructions
echo 4. Exit
echo.

set /p choice="Choose an option (1-4): "

if "%choice%"=="1" goto auto_install
if "%choice%"=="2" goto copy_ffmpeg
if "%choice%"=="3" goto manual_instructions
if "%choice%"=="4" goto exit
goto invalid_choice

:auto_install
echo.
echo ========================================
echo    Automatic FFmpeg Installation
echo ========================================
echo.
echo This will:
echo 1. Download FFmpeg from official source
echo 2. Extract to C:\ffmpeg\
echo 3. Add to system PATH
echo.
echo NOTE: This requires Administrator privileges
echo.
set /p confirm="Continue? (y/n): "
if /i not "%confirm%"=="y" goto menu

echo.
echo Downloading FFmpeg...
echo Please wait, this may take a few minutes...

:: Create ffmpeg directory
if not exist "C:\ffmpeg" mkdir "C:\ffmpeg"
cd /d "C:\ffmpeg"

:: Download FFmpeg (you would need to implement actual download)
echo.
echo [INFO] Automatic download not implemented in this script.
echo Please download manually from: https://ffmpeg.org/download.html
echo.
echo After download:
echo 1. Extract to C:\ffmpeg\
echo 2. Run this script again and choose option 3
echo.
pause
goto menu

:copy_ffmpeg
echo.
echo ========================================
echo    Copy FFmpeg to Application Folder
echo ========================================
echo.
echo This will copy ffmpeg.exe to the current application folder.
echo This bypasses PATH issues but only works for this application.
echo.

set /p ffmpeg_path="Enter full path to ffmpeg.exe: "

if not exist "%ffmpeg_path%" (
    echo [ERROR] File not found: %ffmpeg_path%
    pause
    goto menu
)

copy "%ffmpeg_path%" "%~dp0ffmpeg.exe"
if %errorlevel% == 0 (
    echo [OK] FFmpeg copied successfully!
    echo Testing...
    "%~dp0ffmpeg.exe" -version | findstr "ffmpeg version"
    echo.
    echo FFmpeg is now ready for PHP Twibonize!
) else (
    echo [ERROR] Failed to copy FFmpeg
)
pause
goto menu

:manual_instructions
echo.
echo ========================================
echo    Manual Installation Instructions
echo ========================================
echo.
echo Step 1: Download FFmpeg
echo   - Go to: https://ffmpeg.org/download.html
echo   - Choose "Windows" and download a static build
echo   - Extract the zip file
echo.
echo Step 2: Install FFmpeg
echo   - Copy the extracted folder to C:\ffmpeg\
echo   - The structure should be: C:\ffmpeg\bin\ffmpeg.exe
echo.
echo Step 3: Add to PATH
echo   - Right-click "This PC" ^> Properties
echo   - Click "Advanced system settings"
echo   - Click "Environment Variables"
echo   - Under "System Variables", find and select "Path"
echo   - Click "Edit" ^> "New"
echo   - Add: C:\ffmpeg\bin
echo   - Click "OK" on all dialogs
echo.
echo Step 4: Restart and Test
echo   - Restart XAMPP
echo   - Open Command Prompt
echo   - Type: ffmpeg -version
echo   - Should show FFmpeg version info
echo.
echo Step 5: Test in PHP Twibonize
echo   - Open: http://localhost/php-twibonize/test.php
echo   - FFmpeg should now be detected
echo.
pause
goto menu

:invalid_choice
echo.
echo [ERROR] Invalid choice. Please select 1-4.
pause
goto menu

:menu
cls
goto start

:exit
echo.
echo Exiting...
exit /b 0

:start
goto :eof
