<?php
/**
 * FFmpeg Troubleshooting Tool
 * Membantu mendiagnosis masalah FFmpeg di PHP Twibonize
 */

require_once 'includes/functions.php';

echo "<h2>🔧 FFmpeg Troubleshooting Tool</h2>";

echo "<h3>1. Environment Information</h3>";
echo "<p><strong>OS:</strong> " . PHP_OS . "</p>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";

echo "<h3>2. PATH Environment Variable</h3>";
$path = getenv('PATH');
if ($path) {
    echo "<p><strong>PATH:</strong></p>";
    $pathArray = explode(PATH_SEPARATOR, $path);
    echo "<ul>";
    foreach ($pathArray as $dir) {
        echo "<li>" . htmlspecialchars($dir) . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ PATH environment variable tidak ditemukan</p>";
}

echo "<h3>3. FFmpeg Detection Tests</h3>";

// Test 1: Basic command
echo "<h4>Test 1: Basic 'ffmpeg' command</h4>";
$output1 = [];
$returnCode1 = 0;
exec('ffmpeg -version 2>&1', $output1, $returnCode1);
echo "<p><strong>Return Code:</strong> $returnCode1</p>";
echo "<p><strong>Output:</strong></p>";
echo "<pre>" . htmlspecialchars(implode("\n", array_slice($output1, 0, 5))) . "</pre>";

// Test 2: With .exe extension
echo "<h4>Test 2: 'ffmpeg.exe' command</h4>";
$output2 = [];
$returnCode2 = 0;
exec('ffmpeg.exe -version 2>&1', $output2, $returnCode2);
echo "<p><strong>Return Code:</strong> $returnCode2</p>";
echo "<p><strong>Output:</strong></p>";
echo "<pre>" . htmlspecialchars(implode("\n", array_slice($output2, 0, 5))) . "</pre>";

// Test 3: Where command
echo "<h4>Test 3: 'where ffmpeg' command</h4>";
$output3 = [];
$returnCode3 = 0;
exec('where ffmpeg 2>&1', $output3, $returnCode3);
echo "<p><strong>Return Code:</strong> $returnCode3</p>";
echo "<p><strong>Output:</strong></p>";
echo "<pre>" . htmlspecialchars(implode("\n", $output3)) . "</pre>";

// Test 4: Common paths
echo "<h4>Test 4: Check Common Installation Paths</h4>";
$commonPaths = [
    'C:\\ffmpeg\\bin\\ffmpeg.exe',
    'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
    'C:\\Program Files (x86)\\ffmpeg\\bin\\ffmpeg.exe',
    'C:\\tools\\ffmpeg\\bin\\ffmpeg.exe',
    'C:\\ffmpeg\\ffmpeg.exe',
    getcwd() . '\\ffmpeg.exe'
];

foreach ($commonPaths as $path) {
    if (file_exists($path)) {
        echo "<p>✅ <strong>Found:</strong> $path</p>";
        
        // Test this path
        $testOutput = [];
        $testReturnCode = 0;
        exec('"' . $path . '" -version 2>&1', $testOutput, $testReturnCode);
        echo "<p>&nbsp;&nbsp;&nbsp;Test result: Return code = $testReturnCode</p>";
        if ($testReturnCode === 0 && !empty($testOutput[0])) {
            echo "<p>&nbsp;&nbsp;&nbsp;Version: " . htmlspecialchars($testOutput[0]) . "</p>";
        }
    } else {
        echo "<p>❌ <strong>Not found:</strong> $path</p>";
    }
}

echo "<h3>4. PHP Function Tests</h3>";

// Test validateFFmpegInstallation function
echo "<h4>validateFFmpegInstallation() Result</h4>";
if (validateFFmpegInstallation()) {
    echo "<p>✅ Function returns TRUE - FFmpeg detected</p>";
} else {
    echo "<p>❌ Function returns FALSE - FFmpeg not detected</p>";
}

// Test getFFmpegPath function
echo "<h4>getFFmpegPath() Result</h4>";
$detectedPath = getFFmpegPath();
if ($detectedPath) {
    echo "<p>✅ <strong>Detected Path:</strong> " . htmlspecialchars($detectedPath) . "</p>";
} else {
    echo "<p>❌ No path detected</p>";
}

echo "<h3>5. Recommendations</h3>";

if (!validateFFmpegInstallation()) {
    echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;'>";
    echo "<h4>🚨 FFmpeg Not Detected - Solutions:</h4>";
    echo "<ol>";
    echo "<li><strong>Download FFmpeg:</strong>";
    echo "<ul>";
    echo "<li>Go to <a href='https://ffmpeg.org/download.html' target='_blank'>https://ffmpeg.org/download.html</a></li>";
    echo "<li>Download Windows build (static version recommended)</li>";
    echo "<li>Extract to C:\\ffmpeg\\</li>";
    echo "</ul></li>";
    
    echo "<li><strong>Add to PATH:</strong>";
    echo "<ul>";
    echo "<li>Open System Properties → Advanced → Environment Variables</li>";
    echo "<li>Edit PATH variable</li>";
    echo "<li>Add C:\\ffmpeg\\bin\\ to the PATH</li>";
    echo "<li>Click OK and restart XAMPP</li>";
    echo "</ul></li>";
    
    echo "<li><strong>Alternative - Copy to App Directory:</strong>";
    echo "<ul>";
    echo "<li>Copy ffmpeg.exe to your application directory: " . getcwd() . "</li>";
    echo "<li>This bypasses PATH issues</li>";
    echo "</ul></li>";
    
    echo "<li><strong>Verify Installation:</strong>";
    echo "<ul>";
    echo "<li>Open Command Prompt</li>";
    echo "<li>Type: ffmpeg -version</li>";
    echo "<li>Should show FFmpeg version info</li>";
    echo "</ul></li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h4>✅ FFmpeg Successfully Detected!</h4>";
    echo "<p>Your FFmpeg installation is working correctly.</p>";
    echo "<p>You can now use the video processing features.</p>";
    echo "</div>";
}

echo "<h3>6. Manual Test</h3>";
echo "<p>You can manually test FFmpeg by running this command in Command Prompt:</p>";
echo "<code>ffmpeg -f lavfi -i color=c=red:size=320x240:duration=1 -y test_output.mp4</code>";
echo "<p>This should create a 1-second red video file.</p>";

echo "<h3>7. Quick Actions</h3>";
echo "<p><a href='test.php' class='btn btn-primary'>Run Full System Test</a></p>";
echo "<p><a href='index.php' class='btn btn-success'>Back to Application</a></p>";

if (validateFFmpegInstallation()) {
    echo "<p><a href='demo_template_creator.php' class='btn btn-info'>Create Demo Templates</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>FFmpeg Troubleshooting - PHP Twibonize</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { 
            padding: 20px; 
            font-family: Arial, sans-serif; 
            background-color: #f8f9fa;
        }
        .btn { 
            margin: 5px; 
            padding: 10px 20px; 
        }
        h3 { 
            color: #0d6efd; 
            margin-top: 30px; 
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        h4 {
            color: #6c757d;
            margin-top: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            max-height: 200px;
            overflow-y: auto;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 5px;
            border-radius: 3px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <!-- Content will be echoed above -->
</body>
</html>
