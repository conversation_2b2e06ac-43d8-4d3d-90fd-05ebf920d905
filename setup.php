<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Setup PHP Twibonize</h2>";

// Initialize database
$db = new Database();
$conn = $db->getConnection();

if ($conn) {
    echo "<p>✅ Koneksi database berhasil</p>";
    
    // Create tables
    echo "<p>📋 Membuat tabel database...</p>";
    $db->createTables();
    echo "<p>✅ Tabel database berhasil dibuat</p>";
    
    // Insert sample templates
    echo "<p>🎬 Menambahkan template contoh...</p>";
    $db->insertSampleTemplates();
    echo "<p>✅ Template contoh berhasil ditambahkan</p>";
    
    // Create required directories
    $directories = [
        'uploads/photos',
        'uploads/videos',
        'uploads/temp',
        'assets/templates/videos',
        'assets/templates/previews'
    ];
    
    echo "<p>📁 Membuat direktori yang diperlukan...</p>";
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
            echo "<p>✅ Direktori '$dir' berhasil dibuat</p>";
        } else {
            echo "<p>ℹ️ Direktori '$dir' sudah ada</p>";
        }
    }
    
    // Check FFmpeg installation
    echo "<p>🎥 Memeriksa instalasi FFmpeg...</p>";
    if (validateFFmpegInstallation()) {
        echo "<p>✅ FFmpeg terdeteksi dan siap digunakan</p>";
    } else {
        echo "<p>⚠️ FFmpeg tidak terdeteksi. Silakan install FFmpeg untuk memproses video.</p>";
        echo "<p>Download FFmpeg dari: <a href='https://ffmpeg.org/download.html' target='_blank'>https://ffmpeg.org/download.html</a></p>";
    }
    
    // Create sample template files info
    echo "<h3>📝 Informasi Template</h3>";
    echo "<p>Untuk menggunakan aplikasi ini, Anda perlu:</p>";
    echo "<ol>";
    echo "<li>Menyiapkan file video template di folder 'assets/templates/videos/'</li>";
    echo "<li>Menyiapkan gambar preview template di folder 'assets/templates/previews/'</li>";
    echo "<li>Pastikan nama file sesuai dengan yang ada di database</li>";
    echo "</ol>";
    
    echo "<h4>Template yang perlu disiapkan:</h4>";
    $templates = getVideoTemplates($conn);
    echo "<ul>";
    foreach ($templates as $template) {
        echo "<li>";
        echo "<strong>" . htmlspecialchars($template['name']) . "</strong><br>";
        echo "Video: assets/templates/videos/" . $template['filename'] . "<br>";
        echo "Preview: " . $template['preview_image'] . "<br>";
        echo "Ukuran: " . $template['width'] . "x" . $template['height'] . "px<br>";
        echo "Durasi: " . $template['duration'] . " detik";
        echo "</li><br>";
    }
    echo "</ul>";
    
    echo "<h3>🚀 Setup Selesai!</h3>";
    echo "<p>Aplikasi PHP Twibonize siap digunakan.</p>";
    echo "<p><a href='index.php' class='btn btn-primary'>Buka Aplikasi</a></p>";
    
} else {
    echo "<p>❌ Gagal terhubung ke database. Periksa konfigurasi di config/database.php</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Setup PHP Twibonize</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .btn { margin-top: 20px; padding: 10px 20px; }
    </style>
</head>
<body>
    <!-- Content will be echoed above -->
</body>
</html>
