<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

header('Content-Type: application/json');

// Initialize database connection
$db = new Database();
$conn = $db->getConnection();

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan.');
    }

    // Validate required fields
    if (!isset($_FILES['userPhoto']) || !isset($_POST['template']) || 
        !isset($_POST['photoSize']) || !isset($_POST['photoOpacity'])) {
        throw new Exception('Data tidak lengkap.');
    }

    $templateId = intval($_POST['template']);
    $photoSize = intval($_POST['photoSize']);
    $photoOpacity = intval($_POST['photoOpacity']);

    // Validate template exists
    $template = getTemplateById($conn, $templateId);
    if (!$template) {
        throw new Exception('Template tidak ditemukan.');
    }

    // Validate ranges
    if ($photoSize < 10 || $photoSize > 100) {
        throw new Exception('Ukuran foto harus antara 10-100%.');
    }
    
    if ($photoOpacity < 10 || $photoOpacity > 100) {
        throw new Exception('Transparansi harus antara 10-100%.');
    }

    // Upload user photo
    $userPhotoPath = uploadUserPhoto($_FILES['userPhoto']);

    // Create user video record
    $sessionId = session_id();
    $userVideoId = createUserVideo($conn, $sessionId, $templateId, $userPhotoPath, $photoSize, $photoOpacity);

    // Add to processing queue
    $queueId = addToProcessingQueue($conn, $userVideoId);

    // Return success response with video ID
    echo json_encode([
        'success' => true,
        'message' => 'Video berhasil ditambahkan ke antrian pemrosesan.',
        'userVideoId' => $userVideoId,
        'queueId' => $queueId
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
