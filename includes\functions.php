<?php
/**
 * Fungsi-fungsi utama untuk aplikasi PHP Twibonize
 */

function getVideoTemplates($conn) {
    $query = "SELECT * FROM video_templates ORDER BY created_at DESC";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function uploadUserPhoto($file) {
    $uploadDir = 'uploads/photos/';
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    // Create upload directory if not exists
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Validate file type
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Format file tidak didukung. Gunakan JPG, PNG, atau GIF.');
    }

    // Validate file size
    if ($file['size'] > $maxSize) {
        throw new Exception('Ukuran file terlalu besar. Maksimal 5MB.');
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('photo_') . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Gagal mengupload file.');
    }

    return $filepath;
}

function getTemplateById($conn, $templateId) {
    $query = "SELECT * FROM video_templates WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$templateId]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function createUserVideo($conn, $sessionId, $templateId, $userPhoto, $photoSize, $photoOpacity) {
    $outputFilename = uniqid('video_') . '.mp4';
    $outputPath = 'uploads/videos/' . $outputFilename;

    // Create output directory if not exists
    if (!file_exists('uploads/videos/')) {
        mkdir('uploads/videos/', 0777, true);
    }

    $query = "INSERT INTO user_videos 
              (session_id, template_id, user_photo, output_video, photo_size, photo_opacity, status) 
              VALUES (?, ?, ?, ?, ?, ?, 'processing')";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$sessionId, $templateId, $userPhoto, $outputPath, $photoSize, $photoOpacity]);
    
    return $conn->lastInsertId();
}

function addToProcessingQueue($conn, $userVideoId) {
    $query = "INSERT INTO processing_queue (user_video_id, status) VALUES (?, 'pending')";
    $stmt = $conn->prepare($query);
    $stmt->execute([$userVideoId]);
    return $conn->lastInsertId();
}

function processVideo($conn, $userVideoId) {
    // Get user video details
    $query = "SELECT uv.*, vt.* FROM user_videos uv 
              JOIN video_templates vt ON uv.template_id = vt.id 
              WHERE uv.id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$userVideoId]);
    $videoData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$videoData) {
        throw new Exception('Data video tidak ditemukan.');
    }

    // Update processing status
    updateProcessingStatus($conn, $userVideoId, 'processing', 10);

    try {
        // Resize user photo
        $resizedPhoto = resizeUserPhoto(
            $videoData['user_photo'], 
            $videoData['photo_width'], 
            $videoData['photo_height'],
            $videoData['photo_size']
        );
        
        updateProcessingStatus($conn, $userVideoId, 'processing', 30);

        // Create overlay video using FFmpeg
        $success = createVideoWithOverlay(
            $videoData['filename'],
            $resizedPhoto,
            $videoData['output_video'],
            $videoData['photo_position_x'],
            $videoData['photo_position_y'],
            $videoData['photo_opacity'],
            $userVideoId,
            $conn
        );

        if ($success) {
            // Update status to completed
            $updateQuery = "UPDATE user_videos SET status = 'completed' WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->execute([$userVideoId]);

            updateProcessingStatus($conn, $userVideoId, 'completed', 100);
            
            // Clean up temporary files
            if (file_exists($resizedPhoto)) {
                unlink($resizedPhoto);
            }
            
            return true;
        } else {
            throw new Exception('Gagal memproses video.');
        }
    } catch (Exception $e) {
        // Update status to failed
        $updateQuery = "UPDATE user_videos SET status = 'failed' WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->execute([$userVideoId]);

        updateProcessingStatus($conn, $userVideoId, 'failed', 0, $e->getMessage());
        throw $e;
    }
}

function resizeUserPhoto($photoPath, $targetWidth, $targetHeight, $sizePercentage) {
    // Calculate new dimensions based on percentage
    $newWidth = intval($targetWidth * ($sizePercentage / 100));
    $newHeight = intval($targetHeight * ($sizePercentage / 100));

    // Get image info
    $imageInfo = getimagesize($photoPath);
    $imageType = $imageInfo[2];

    // Create image resource based on type
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($photoPath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($photoPath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($photoPath);
            break;
        default:
            throw new Exception('Format gambar tidak didukung.');
    }

    // Create new image with target dimensions
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefill($newImage, 0, 0, $transparent);
    }

    // Resize image
    imagecopyresampled(
        $newImage, $sourceImage,
        0, 0, 0, 0,
        $newWidth, $newHeight,
        imagesx($sourceImage), imagesy($sourceImage)
    );

    // Save resized image
    $resizedPath = 'uploads/temp/' . uniqid('resized_') . '.png';
    
    // Create temp directory if not exists
    if (!file_exists('uploads/temp/')) {
        mkdir('uploads/temp/', 0777, true);
    }

    imagepng($newImage, $resizedPath);

    // Clean up memory
    imagedestroy($sourceImage);
    imagedestroy($newImage);

    return $resizedPath;
}

function createVideoWithOverlay($templatePath, $overlayImagePath, $outputPath, $x, $y, $opacity, $userVideoId, $conn) {
    // Full path to template
    $fullTemplatePath = 'assets/templates/videos/' . $templatePath;
    
    if (!file_exists($fullTemplatePath)) {
        throw new Exception('Template video tidak ditemukan: ' . $fullTemplatePath);
    }

    // FFmpeg command to overlay image on video
    $opacityValue = $opacity / 100;
    
    $command = sprintf(
        'ffmpeg -i "%s" -i "%s" -filter_complex "[1:v]format=rgba,colorchannelmixer=aa=%f[overlay];[0:v][overlay]overlay=%d:%d" -c:a copy -y "%s" 2>&1',
        $fullTemplatePath,
        $overlayImagePath,
        $opacityValue,
        $x,
        $y,
        $outputPath
    );

    // Execute FFmpeg command
    $output = [];
    $returnCode = 0;
    
    exec($command, $output, $returnCode);
    
    // Update progress during processing
    updateProcessingStatus($conn, $userVideoId, 'processing', 80);
    
    if ($returnCode === 0 && file_exists($outputPath)) {
        return true;
    } else {
        $errorMessage = implode("\n", $output);
        throw new Exception('FFmpeg error: ' . $errorMessage);
    }
}

function updateProcessingStatus($conn, $userVideoId, $status, $progress, $errorMessage = null) {
    $query = "UPDATE processing_queue 
              SET status = ?, progress = ?, error_message = ?, updated_at = NOW() 
              WHERE user_video_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$status, $progress, $errorMessage, $userVideoId]);
}

function getProcessingStatus($conn, $userVideoId) {
    $query = "SELECT * FROM processing_queue WHERE user_video_id = ? ORDER BY created_at DESC LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->execute([$userVideoId]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getUserVideo($conn, $userVideoId) {
    $query = "SELECT * FROM user_videos WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$userVideoId]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function cleanupOldFiles($conn, $daysOld = 7) {
    $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
    
    // Get old videos
    $query = "SELECT * FROM user_videos WHERE created_at < ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$cutoffDate]);
    $oldVideos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($oldVideos as $video) {
        // Delete files
        if (file_exists($video['user_photo'])) {
            unlink($video['user_photo']);
        }
        if (file_exists($video['output_video'])) {
            unlink($video['output_video']);
        }
    }
    
    // Delete database records
    $deleteQuery = "DELETE FROM user_videos WHERE created_at < ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->execute([$cutoffDate]);
    
    return count($oldVideos);
}

function validateFFmpegInstallation() {
    $output = [];
    $returnCode = 0;
    exec('ffmpeg -version 2>&1', $output, $returnCode);
    
    return $returnCode === 0;
}
?>
