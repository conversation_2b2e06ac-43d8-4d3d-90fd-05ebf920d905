<?php
/**
 * Demo Template Creator
 * Membuat template video sederhana untuk demonstrasi
 */

echo "<h2>🎬 Demo Template Creator</h2>";

require_once 'includes/functions.php';

// Check if FFmpeg is available
if (!validateFFmpegInstallation()) {
    echo "<p>❌ FFmpeg tidak tersedia. Install FFmpeg terlebih dahulu.</p>";
    $ffmpegPath = getFFmpegPath();
    if ($ffmpegPath) {
        echo "<p>ℹ️ FFmpeg ditemukan di: " . htmlspecialchars($ffmpegPath) . "</p>";
    } else {
        echo "<p>💡 Pastikan FFmpeg sudah terinstall dan ada di PATH sistem.</p>";
    }
    exit;
}

// Create simple colored background videos as templates
$templates = [
    [
        'name' => 'Template Merah',
        'filename' => 'template_red.mp4',
        'color' => 'red',
        'duration' => 5
    ],
    [
        'name' => 'Template Biru', 
        'filename' => 'template_blue.mp4',
        'color' => 'blue',
        'duration' => 8
    ],
    [
        'name' => 'Template Hijau',
        'filename' => 'template_green.mp4', 
        'color' => 'green',
        'duration' => 10
    ]
];

echo "<h3>Membuat Template Video Demo...</h3>";

// Get FFmpeg path
$ffmpegPath = getFFmpegPath();
if (!$ffmpegPath) {
    echo "<p>❌ Tidak dapat menemukan path FFmpeg</p>";
    exit;
}

foreach ($templates as $template) {
    $outputPath = 'assets/templates/videos/' . $template['filename'];

    // Create colored background video using FFmpeg
    $command = sprintf(
        '"%s" -f lavfi -i color=c=%s:size=1920x1080:duration=%d -c:v libx264 -pix_fmt yuv420p -y "%s" 2>&1',
        $ffmpegPath,
        $template['color'],
        $template['duration'],
        $outputPath
    );

    echo "<p>Membuat " . $template['name'] . "...</p>";

    $output = [];
    $returnCode = 0;
    exec($command, $output, $returnCode);

    if ($returnCode === 0 && file_exists($outputPath)) {
        echo "<p>✅ " . $template['name'] . " berhasil dibuat</p>";

        // Create preview image
        $previewPath = 'assets/templates/previews/' . str_replace('.mp4', '.jpg', $template['filename']);
        $previewCommand = sprintf(
            '"%s" -i "%s" -ss 1 -vframes 1 -y "%s" 2>&1',
            $ffmpegPath,
            $outputPath,
            $previewPath
        );

        exec($previewCommand, $previewOutput, $previewReturnCode);

        if ($previewReturnCode === 0 && file_exists($previewPath)) {
            echo "<p>✅ Preview " . $template['name'] . " berhasil dibuat</p>";
        }

    } else {
        echo "<p>❌ Gagal membuat " . $template['name'] . "</p>";
        echo "<p>Error: " . implode("\n", $output) . "</p>";
    }
}

echo "<h3>✅ Demo template selesai dibuat!</h3>";
echo "<p>Template video demo telah dibuat di folder assets/templates/videos/</p>";
echo "<p>Gambar preview telah dibuat di folder assets/templates/previews/</p>";
echo "<p><a href='index.php' class='btn btn-primary'>Test Aplikasi</a></p>";


?>

<!DOCTYPE html>
<html>
<head>
    <title>Demo Template Creator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .btn { margin-top: 20px; padding: 10px 20px; }
    </style>
</head>
<body>
    <!-- Content will be echoed above -->
</body>
</html>
