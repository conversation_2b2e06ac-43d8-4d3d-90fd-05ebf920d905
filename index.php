<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Set timezone
date_default_timezone_set('Asia/Jakarta');

// Initialize database connection
$db = new Database();
$conn = $db->getConnection();

// Get available templates
$templates = getVideoTemplates($conn);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP Twibonize - Gabungkan Foto dengan Video Template</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-video me-2"></i>PHP Twibonize
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-magic me-2"></i>Buat Video Twibbon Anda</h4>
                    </div>
                    <div class="card-body">
                        <form id="twibbonForm" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="userPhoto" class="form-label">
                                            <i class="fas fa-image me-1"></i>Upload Foto Anda
                                        </label>
                                        <input type="file" class="form-control" id="userPhoto" name="userPhoto" 
                                               accept="image/*" required>
                                        <div class="form-text">Format: JPG, PNG, GIF. Maksimal 5MB</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="template" class="form-label">
                                            <i class="fas fa-film me-1"></i>Pilih Template Video
                                        </label>
                                        <select class="form-select" id="template" name="template" required>
                                            <option value="">Pilih Template...</option>
                                            <?php foreach ($templates as $template): ?>
                                                <option value="<?= $template['id'] ?>" 
                                                        data-preview="<?= $template['preview_image'] ?>">
                                                    <?= htmlspecialchars($template['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-cog me-1"></i>Pengaturan
                                        </label>
                                        <div class="row">
                                            <div class="col-6">
                                                <label for="photoSize" class="form-label">Ukuran Foto (%)</label>
                                                <input type="range" class="form-range" id="photoSize" 
                                                       name="photoSize" min="10" max="100" value="50">
                                                <span id="photoSizeValue">50%</span>
                                            </div>
                                            <div class="col-6">
                                                <label for="photoOpacity" class="form-label">Transparansi (%)</label>
                                                <input type="range" class="form-range" id="photoOpacity" 
                                                       name="photoOpacity" min="10" max="100" value="100">
                                                <span id="photoOpacityValue">100%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-magic me-2"></i>Buat Video Twibbon
                                    </button>
                                </div>

                                <div class="col-md-6">
                                    <div class="preview-section">
                                        <h5><i class="fas fa-eye me-1"></i>Preview</h5>
                                        
                                        <div id="photoPreview" class="preview-box mb-3" style="display: none;">
                                            <img id="photoPreviewImg" src="" alt="Preview Foto" class="img-fluid">
                                        </div>

                                        <div id="templatePreview" class="preview-box mb-3" style="display: none;">
                                            <img id="templatePreviewImg" src="" alt="Preview Template" class="img-fluid">
                                            <div class="template-info mt-2">
                                                <small class="text-muted">Template yang dipilih</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div id="progressSection" style="display: none;" class="mt-4">
                            <h5><i class="fas fa-cogs me-1"></i>Memproses Video...</h5>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressText" class="text-center mt-2">Memulai proses...</div>
                        </div>

                        <div id="resultSection" style="display: none;" class="mt-4">
                            <h5><i class="fas fa-check-circle text-success me-1"></i>Video Berhasil Dibuat!</h5>
                            <div class="result-video">
                                <video id="resultVideo" controls class="w-100 mb-3">
                                    <source id="resultVideoSource" src="" type="video/mp4">
                                    Browser Anda tidak mendukung video HTML5.
                                </video>
                                <div class="text-center">
                                    <a id="downloadLink" href="" class="btn btn-primary btn-lg" download>
                                        <i class="fas fa-download me-2"></i>Download Video
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-images me-1"></i>Template Video Tersedia</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($templates as $template): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="template-card">
                                        <img src="<?= $template['preview_image'] ?>" 
                                             alt="<?= htmlspecialchars($template['name']) ?>" 
                                             class="img-fluid rounded">
                                        <div class="template-info mt-2">
                                            <h6><?= htmlspecialchars($template['name']) ?></h6>
                                            <small class="text-muted"><?= $template['duration'] ?>s</small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="row mt-4">
            <div class="col-md-10 mx-auto">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-question-circle me-1"></i>Butuh Bantuan?</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-2">
                                <a href="test.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-check-circle me-1"></i>Test Sistem
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="ffmpeg_troubleshoot.php" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-tools me-1"></i>FFmpeg Troubleshoot
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="demo_template_creator.php" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-video me-1"></i>Buat Template Demo
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="setup.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-cog me-1"></i>Setup Ulang
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 PHP Twibonize. Dibuat dengan <i class="fas fa-heart text-danger"></i></p>
            <small>
                <a href="README.md" class="text-light me-2">Dokumentasi</a> |
                <a href="INSTALL.md" class="text-light me-2">Panduan Install</a> |
                <a href="FFMPEG_SOLUTION.md" class="text-light">Solusi FFmpeg</a>
            </small>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/script.js"></script>
</body>
</html>
